---
# Traefik Configuration with Multiple Instances and Sticky Sessions
# Supports WebSocket connections and document processing with session affinity

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    hostname: localhost
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API docs port
      - "8202:8202"   # Traefik dashboard port
    # Optimized logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"  # Reduced from 50m
        max-file: "3"    # Reduced from 5
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      - /var/log/eko:/var/log/traefik
      - /etc/letsencrypt/:/etc/letsencrypt/:ro
      - /etc/hosts:/etc/hosts:ro
    environment:
      - DOMAIN=${DOMAIN}
    # Optimized resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    command:
      - /bin/sh
      - -c
      - |
        echo "🔍 Checking for port conflicts..."
        REQUIRED_PORTS="8201 8202 8203"
        PORT_CONFLICTS=""
        for port in $$REQUIRED_PORTS; do
          if netstat -tuln 2>/dev/null | grep -q ":$$port "; then
            PORT_CONFLICTS="$$PORT_CONFLICTS $$port"
          fi
        done
        if [ -n "$$PORT_CONFLICTS" ]; then
          echo "❌ Port conflicts detected on ports:$$PORT_CONFLICTS"
          exit 1
        else
          echo "✅ All required ports (8201-8203) are available"
        fi

        echo "🌐 DOMAIN: $$DOMAIN"
        CERT_DIR="/etc/letsencrypt/live/$$DOMAIN"
        if [ -d "$$CERT_DIR" ]; then
          echo "✅ Certificate directory found: $$CERT_DIR"
          echo "🔧 Generating dynamic TLS configuration..."
          cat > /etc/traefik/conf/dynamic-tls.yaml << EOF
        tls:
          certificates:
            - certFile: $$CERT_DIR/fullchain.pem
              keyFile: $$CERT_DIR/privkey.pem
        EOF
          echo "✅ Dynamic TLS configuration created"
        else
          echo "❌ Certificate directory not found: $$CERT_DIR"
        fi

        echo "🚀 Starting Traefik..."
        traefik --configfile=/etc/traefik/traefik.yaml
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"
      - "com.docker.compose.project=eko"

      # HTTP to HTTPS redirect - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.http-redirect.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.entrypoints=web}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.middlewares=https-redirect@file}"

      # Traefik dashboard on port 8202 - works with or without domain
      - "traefik.http.routers.traefik-dashboard.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=traefik-dashboard"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.middlewares=api-cors@file"

  # Dedicated Document Processing Instances (1-2 instances for WebSocket + process-documents)
  # These instances handle ONLY document processing flow with guaranteed sticky sessions
  eko-api-docs:
    build: .
    image: eko-backend:latest
    hostname: diwas
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-docs
      - INSTANCE_ID=${HOSTNAME:-eko-api-docs}
      - INSTANCE_TYPE=document-processing
      - TZ=Asia/Kathmandu
      - LOG_LEVEL=INFO  # Reduced from DEBUG
      - ENABLE_REQUEST_LOGGING=false  # Disabled for performance
      - ENABLE_RESPONSE_LOGGING=false # Disabled for performance
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - /var/log/eko:/var/log/eko
      - /etc/hosts:/etc/hosts:ro
    networks:
      - eko-network
    # Optimized resource limits
    deploy:
      resources:
        limits:
          memory: 1G      # Reduced from default
          cpus: '1.0'     # Limited CPU usage
        reservations:
          memory: 512M
          cpus: '0.5'
    # Optimized logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Optimized health checks
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health-minimal"]
      interval: 30s
      timeout: 5s
      retries: 2
      start_period: 10s  # Reduced from default 60s
    labels:
      - "traefik.enable=true"
      - "com.docker.compose.project=eko"

      # CRITICAL: Document processing endpoints - DEDICATED instances only
      # Process documents endpoint - highest priority, dedicated service
      - "traefik.http.routers.process-docs.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/process-documents`)"
      - "traefik.http.routers.process-docs.entrypoints=websecure,http-api"
      - "traefik.http.routers.process-docs.service=eko-docs-service"
      - "traefik.http.routers.process-docs.middlewares=api-cors@file,document-headers@file"
      - "traefik.http.routers.process-docs.priority=100"
      - "${DOMAIN:+traefik.http.routers.process-docs.tls=true}"

      # WebSocket setup_files endpoint - SAME dedicated instances as process-documents
      - "traefik.http.routers.websocket.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket.entrypoints=websecure,http-api"
      - "traefik.http.routers.websocket.service=eko-docs-service"
      - "traefik.http.routers.websocket.middlewares=websocket-headers@file,api-cors@file"
      - "traefik.http.routers.websocket.priority=99"
      - "${DOMAIN:+traefik.http.routers.websocket.tls=true}"

      # Check status endpoint - SAME dedicated instances
      - "traefik.http.routers.check-status.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/check_status`)"
      - "traefik.http.routers.check-status.entrypoints=websecure,http-api"
      - "traefik.http.routers.check-status.service=eko-docs-service"
      - "traefik.http.routers.check-status.middlewares=api-cors@file"
      - "traefik.http.routers.check-status.priority=98"
      - "${DOMAIN:+traefik.http.routers.check-status.tls=true}"

      # Optimized service configuration with faster health checks
      - "traefik.http.services.eko-docs-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.path=/health-minimal"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.interval=15s"  # Faster checks
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.timeout=3s"   # Faster timeout
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.scheme=http"

      # Session stickiness with cookies - CRITICAL for document processing flow
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.name=eko-docs-session"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.secure=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.httpOnly=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.sameSite=none"

      # Load balancer strategy - ensures consistent routing
      - "traefik.http.services.eko-docs-service.loadbalancer.passhostheader=true"

  # General API Instances (4+ instances for all other traffic)
  # Use: docker-compose up --scale eko-api=4 --scale eko-api-docs=1 -d
  eko-api:
    build: .
    image: eko-backend:latest
    hostname: diwas
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
      - INSTANCE_ID=${HOSTNAME:-eko-api}
      - INSTANCE_TYPE=general
      - TZ=Asia/Kathmandu
      - LOG_LEVEL=INFO
      - ENABLE_REQUEST_LOGGING=false
      - ENABLE_RESPONSE_LOGGING=false
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - /var/log/eko:/var/log/eko
      - /etc/hosts:/etc/hosts:ro
    networks:
      - eko-network
    # Optimized resource limits
    deploy:
      resources:
        limits:
          memory: 800M
          cpus: '0.8'
        reservations:
          memory: 400M
          cpus: '0.4'
    # Optimized logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Optimized health checks
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health-minimal"]
      interval: 30s
      timeout: 5s
      retries: 2
      start_period: 10s
    labels:
      - "traefik.enable=true"
      - "com.docker.compose.project=eko"

      # Main HTTPS API routes - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.api-secure.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.api-secure.entrypoints=websecure}"
      - "${DOMAIN:+traefik.http.routers.api-secure.tls=true}"
      - "${DOMAIN:+traefik.http.routers.api-secure.service=eko-api-service}"
      - "${DOMAIN:+traefik.http.routers.api-secure.middlewares=default-headers@file,api-cors@file}"

      # General API routes - handles all traffic EXCEPT document processing
      - "traefik.http.routers.api-docs.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.api-docs.entrypoints=http-api"
      - "traefik.http.routers.api-docs.service=eko-api-service"
      - "traefik.http.routers.api-docs.middlewares=api-cors@file,compression@file"
      - "traefik.http.routers.api-docs.priority=1"

      # Optimized service configuration
      - "traefik.http.services.eko-api-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.path=/health-minimal"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.interval=15s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.timeout=3s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.scheme=http"

      # Load balancer strategy for general traffic
      - "traefik.http.services.eko-api-service.loadbalancer.passhostheader=true"





networks:
  eko-network:
    driver: bridge


