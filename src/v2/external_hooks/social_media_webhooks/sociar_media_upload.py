from io import By<PERSON><PERSON>
from typing import List, Dict
import asyncio
import httpx
from requests_toolbelt import MultipartEncoder

from src.models.user import UserTenantDB
from src.reply.minio_client import MinIOClient
from src.v2.external_hooks.social_media_webhooks.models import MediaItem
from fastapi import HTTPEx<PERSON>,APIRouter

router = APIRouter()

@router.post("/upload_to_sociair", response_model=List[str])
async def upload_to_sociair(media_urls: List[MediaItem], current_user: UserTenantDB) -> List[str]:
    """
    Uploads media files from MinIO to Sociair's media upload endpoint.

    :param media_urls: List of MinIO object names (files) to upload.
    :param current_user: Authenticated user with access to minio_config and async_db.
    :return: List of Sociair media IDs.
    """
    minio_client = MinIOClient(**current_user.minio_config)

    access_token_doc = await current_user.async_db.settings.find_one(
        {"name": "sociar_env"},
        {"API_TOKEN": 1, "BASE_URL": 1, "_id": 0}
    )

    url = f"{access_token_doc['BASE_URL']}/master/medias/upload"
    base_headers = {
        'Authorization': f'Bearer {access_token_doc["API_TOKEN"]}',
        'Accept': 'application/json, text/plain, */*',
    }

    tasks = [
        upload_to_sociair_bytes(
            await asyncio.to_thread(minio_client.get_object_bytes,  # non-blocking
                bucket_name=current_user.minio_config["bucket_name"],
                object_name=media.name
            ),
            base_headers,
            url
        )
        for media in media_urls
    ]

    responses = await asyncio.gather(*tasks)

    # Extract valid media IDs
    sociair_ids = [resp.get("id") for resp in responses if resp and resp.get("id")]
    return sociair_ids


async def upload_to_sociair_bytes(file_bytes: bytes, base_headers: Dict[str, str], url: str) -> dict:
    """
    Uploads a single file (in bytes) to Sociair's media upload endpoint.

    :param file_bytes: The bytes of the file to be uploaded.
    :param base_headers: Base headers including Authorization.
    :param url: The full URL for uploading.
    :return: Response from Sociair API.
    """
    file_like = BytesIO(file_bytes)
    file_like.seek(0)

    multipart_data = MultipartEncoder(
        fields={
            'uploaded_file[0]': ('Screenshot.png', file_like, 'image/png'),
            'upload_for': 'media'
        }
    )

    headers = {
        **base_headers,
        'Content-Type': multipart_data.content_type
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, content=multipart_data)

    return response.json()
