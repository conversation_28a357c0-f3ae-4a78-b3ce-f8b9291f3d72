# ============================================================================
# OPTIMIZED MIDDLEWARE CONFIGURATION FOR PERFORMANCE
# ============================================================================

http:
  middlewares:
    # Optimized CORS middleware
    api-cors:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "*"
        accessControlExposeHeaders:
          - "*"
        accessControlAllowCredentials: true
        accessControlMaxAge: 86400  # Cache preflight for 24 hours

    # Lightweight compression
    compression:
      excludedContentTypes:
        - "text/event-stream"
        - "application/octet-stream"
        - "image/*"
        - "video/*"
        - "audio/*"
      minResponseBodyBytes: 1024  # Only compress larger responses

    # Optimized security headers
    default-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        # Removed CSP for performance (add if needed)

    # Fast HTTPS redirect
    https-redirect:
      redirectScheme:
        scheme: https
        permanent: true

    # Optimized rate limiting for API endpoints
    api-rate-limit:
      rateLimit:
        burst: 100
        average: 50
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # Lightweight rate limiting for document processing
    document-rate-limit:
      rateLimit:
        burst: 10
        average: 5
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # Optimized WebSocket headers
    websocket-headers:
      headers:
        customRequestHeaders:
          Connection: "upgrade"
          Upgrade: "websocket"
        customResponseHeaders:
          X-WebSocket-Support: "enabled"

    # Document processing headers
    document-headers:
      headers:
        customResponseHeaders:
          X-Content-Type-Options: "nosniff"
          X-Frame-Options: "DENY"
          Cache-Control: "no-cache, no-store, must-revalidate"

    # Circuit breaker for resilience
    circuit-breaker:
      circuitBreaker:
        expression: "NetworkErrorRatio() > 0.3 || ResponseCodeRatio(500, 600, 0, 600) > 0.3"
        checkPeriod: "10s"
        fallbackDuration: "30s"
        recoveryDuration: "60s"

    # Retry middleware for failed requests
    retry:
      retry:
        attempts: 3
        initialInterval: "100ms"

    # Request timeout
    timeout:
      forwardingTimeouts:
        dialTimeout: "5s"
        responseHeaderTimeout: "10s"
        idleConnTimeout: "90s"

    # IP whitelist for admin endpoints (optional)
    admin-whitelist:
      ipWhiteList:
        sourceRange:
          - "127.0.0.1/32"
          - "10.0.0.0/8"
          - "**********/12"
          - "***********/16"

    # Buffering for large requests
    buffering:
      buffering:
        maxRequestBodyBytes: 10485760  # 10MB
        memRequestBodyBytes: 2097152   # 2MB
        maxResponseBodyBytes: 10485760 # 10MB
        memResponseBodyBytes: 2097152  # 2MB
        retryExpression: "IsNetworkError() && Attempts() < 3"
