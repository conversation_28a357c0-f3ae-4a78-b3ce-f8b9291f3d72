# ============================================================================
# OPTIMIZED TRAEFIK CONFIGURATION FOR FAST STARTUP AND LOW RESOURCE USAGE
# ============================================================================

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and dashboard configuration
api:
  dashboard: true
  debug: false  # Disabled for performance
  insecure: true

# Entry points with optimized settings
entryPoints:
  web:
    address: ":80"
    # Optimized transport settings
    transport:
      respondingTimeouts:
        readTimeout: "30s"
        writeTimeout: "30s"
        idleTimeout: "60s"
  
  websecure:
    address: ":443"
    transport:
      respondingTimeouts:
        readTimeout: "30s"
        writeTimeout: "30s"
        idleTimeout: "60s"
  
  http-api:
    address: ":8201"
    transport:
      respondingTimeouts:
        readTimeout: "15s"  # Faster for API calls
        writeTimeout: "15s"
        idleTimeout: "30s"
  
  traefik-dashboard:
    address: ":8202"
    transport:
      respondingTimeouts:
        readTimeout: "10s"
        writeTimeout: "10s"
        idleTimeout: "30s"

# Optimized providers configuration
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true
    # Optimized polling
    pollInterval: "10s"  # Reduced from default 15s
    pollTimeout: "5s"    # Reduced from default 15s
    
  file:
    directory: /etc/traefik/conf
    watch: true

# Optimized logging
log:
  level: "WARN"  # Reduced from INFO/DEBUG
  filePath: "/var/log/traefik/traefik.log"
  format: "json"

# Optimized access logs
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: "json"
  # Reduced logging for performance
  filters:
    statusCodes: ["400-599"]  # Only log errors
    retryAttempts: true
    minDuration: "100ms"      # Only log slow requests

# Optimized metrics (optional)
metrics:
  prometheus:
    addEntryPointsLabels: false
    addServicesLabels: false
    addRoutersLabels: false
    # Reduced buckets for better performance
    buckets:
      - 0.1
      - 0.3
      - 1.2
      - 5.0

# Optimized ping endpoint
ping:
  entryPoint: "traefik-dashboard"

# Optimized health check settings
healthcheck:
  interval: "30s"
  timeout: "5s"

# Optimized TLS settings
tls:
  options:
    default:
      minVersion: "VersionTLS12"
      maxVersion: "VersionTLS13"
      # Optimized cipher suites for performance
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"

# Optimized certificate resolvers (if using Let's Encrypt)
certificatesResolvers:
  letsencrypt:
    acme:
      email: "<EMAIL>"
      storage: "/etc/letsencrypt/acme.json"
      # Use HTTP challenge for faster resolution
      httpChallenge:
        entryPoint: "web"
      # Optimized settings
      caServer: "https://acme-v02.api.letsencrypt.org/directory"
      keyType: "EC256"  # Faster than RSA

# Optimized experimental features
experimental:
  # Enable HTTP/3 for better performance (optional)
  http3: false  # Disable if not needed to reduce complexity
